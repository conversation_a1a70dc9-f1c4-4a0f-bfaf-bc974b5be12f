import { useState } from 'react';
import { ethers } from 'ethers';
import { ROOTSTOCK_MAINNET, ROOTSTOCK_TESTNET } from '../utils/chains';

declare global {
  interface Window {
    ethereum?: any;
  }
}

type NetworkType = 'mainnet' | 'testnet';
type WalletType = 'MetaMask' | 'Rabby' | 'Edge' | 'Ledger' | 'Bitget' | 'Phantom' | 'Unknown' ;

export default function useRootstockWallet() {
  const [provider, setProvider] = useState<ethers.BrowserProvider | null>(null);
  const [signer, setSigner] = useState<ethers.JsonRpcSigner | null>(null);
  const [address, setAddress] = useState<string | null>(null);
  const [balance, setBalance] = useState<string | null>(null);
  const [walletType, setWalletType] = useState<WalletType>('Unknown');
  const [network, setNetwork] = useState<NetworkType>('mainnet');

const getInjectedProvider = (selectedWallet: WalletType) => {
  const ethereum = window.ethereum;

  if (!ethereum) return null;

  // ✅ 1. Check for multiple injected providers
  if (Array.isArray(ethereum.providers)) {
    return ethereum.providers.find((p: any) => {
      if (selectedWallet === 'MetaMask') return p.isMetaMask;
      if (selectedWallet === 'Rabby') return p.isRabby;
      if (selectedWallet === 'Edge') return p.isEdge;
      if (selectedWallet === 'Ledger') return p.isLedger;
      if (selectedWallet === 'Bitget') return p.isBitget;
      if (selectedWallet === 'Phantom') return p.isPhantom;
      return false;
    });
  }

  // ✅ 2. If only one provider exists
  if (selectedWallet === 'MetaMask' && ethereum.isMetaMask) return ethereum;
  if (selectedWallet === 'Rabby' && ethereum.isRabby) return ethereum;
  if (selectedWallet === 'Edge' && ethereum.isEdge) return ethereum;
  if (selectedWallet === 'Ledger' && ethereum.isLedger) return ethereum;
  if (selectedWallet === 'Bitget' && ethereum.isBitget) return ethereum;
  if (selectedWallet === 'Phantom' && ethereum.isPhantom) return ethereum;

  return null;
};


  const connectWallet = async (wallet: WalletType, networkType: NetworkType) => {
    const injected = getInjectedProvider(wallet);
    if (!injected) {
      alert(`${wallet} not found`);
      return;
    }

    setWalletType(wallet);
    setNetwork(networkType);

    const chain = networkType === 'mainnet' ? ROOTSTOCK_MAINNET : ROOTSTOCK_TESTNET;

    try {
      await injected.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: chain.chainId }],
      });
    } catch (error: any) {
      if (error.code === 4902) {
        await injected.request({
          method: 'wallet_addEthereumChain',
          params: [chain],
        });
      }
    }

    const ethProvider = new ethers.BrowserProvider(injected);
    setProvider(ethProvider);

    await ethProvider.send('eth_requestAccounts', []);
    const ethSigner = await ethProvider.getSigner();
    setSigner(ethSigner);

    const userAddress = await ethSigner.getAddress();
    setAddress(userAddress);

    const bal = await ethProvider.getBalance(userAddress);
    setBalance(ethers.formatEther(bal));
  };

  const disconnectWallet = () => {
    setSigner(null);
    setAddress(null);
    setBalance(null);
  };

  const switchNetwork = async (target: NetworkType) => {
    await connectWallet(walletType, target);
  };

  return {
    connectWallet,
    disconnectWallet,
    switchNetwork,
    address,
    balance,
    walletType,
    network,
    isConnected: !!address,
  };
}
