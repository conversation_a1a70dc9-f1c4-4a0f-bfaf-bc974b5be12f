import React from 'react';

interface Props {
  onConnect: (wallet: 'MetaMask' | 'Rabby' | 'Edge' | 'Ledger' | 'Bitget' | 'Phantom', network: 'mainnet' | 'testnet') => void;
}

export const WalletSelector: React.FC<Props> = ({ onConnect }) => {
  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-60 flex items-center justify-center">
      <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-md">
        <h2 className="text-xl font-semibold mb-4">Select Wallet</h2>
        <div className="space-y-2">
          <button className="w-full py-2 px-4 bg-blue-600 text-white rounded hover:bg-blue-700" onClick={() => onConnect('MetaMask', 'mainnet')}>MetaMask </button>
          <button className="w-full py-2 px-4 bg-purple-600 text-white rounded hover:bg-purple-700" onClick={() => onConnect('Rabby', 'mainnet')}>Rabby </button>
          <button className="w-full py-2 px-4 bg-green-600 text-white rounded hover:bg-green-700" onClick={() => onConnect('Edge', 'mainnet')}>Edge </button>
          <button className="w-full py-2 px-4 bg-yellow-600 text-white rounded hover:bg-yellow-700" onClick={() => onConnect('Ledger', 'mainnet')}>Ledger </button>
          <button className="w-full py-2 px-4 bg-red-600 text-white rounded hover:bg-red-700" onClick={() => onConnect('Bitget', 'mainnet')}>Bitget </button>
          <button className="w-full py-2 px-4 bg-orange-600 text-white rounded hover:bg-orange-700" onClick={() => onConnect('Phantom', 'mainnet')}>Phantom </button>
        </div>
      </div>
    </div>
  );
};