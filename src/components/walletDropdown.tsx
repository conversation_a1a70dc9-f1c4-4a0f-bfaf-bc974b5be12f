import React, { useState } from "react";

interface Props {
  address: string;
  balance: string | null;
  walletType: string | null;
  network: string;
  onDisconnect: () => void;
  onSwitch: () => void;
}

export const WalletDropdown: React.FC<Props> = ({
  address,
  balance,
  walletType,
  network,
  onDisconnect,
  onSwitch,
}) => {
  const [open, setOpen] = useState(false);
  const shortAddress = `${address.slice(0, 6)}...${address.slice(-4)}`;

  return (
    <div className="relative inline-block text-left">
      <button
        onClick={() => setOpen(!open)}
        className="px-4 py-2 bg-gray-800 text-white rounded-md shadow-md hover:bg-gray-700 flex items-center gap-2"
      >
        {shortAddress}
        <svg
          className={`w-4 h-4 transition-transform ${
            open ? "rotate-180" : "rotate-0"
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {open && (
        <div className="absolute right-0 mt-2 w-64 bg-white border border-gray-200 rounded-md shadow-lg p-4 z-10">
          <p className="text-sm mb-2">
            <strong>Wallet:</strong> {walletType}
          </p>
          <p className="text-sm mb-2">
            <strong>Balance:</strong> {Number(balance).toFixed(5)} RBTC
          </p>
          <p className="text-sm mb-4">
            <strong>Network:</strong> {network}
          </p>
          <button
            onClick={onSwitch}
            className="block w-full text-left py-2 px-3 rounded bg-yellow-500 text-white hover:bg-yellow-600 mb-2"
          >
            Switch Network
          </button>
          <button
            onClick={onDisconnect}
            className="block w-full text-left py-2 px-3 rounded bg-red-500 text-white hover:bg-red-600"
          >
            Disconnect
          </button>
        </div>
      )}
    </div>
  );
};
