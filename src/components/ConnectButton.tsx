import { useState } from 'react';
import useRootstockWallet from '../hooks/useRootstockWallet';
import { WalletSelector } from './walletSelector';
import { WalletDropdown } from './walletDropdown';

export const ConnectButton = () => {
  const {
    connectWallet,
    disconnectWallet,
    switchNetwork,
    address,
    balance,
    walletType,
    network,
    isConnected,
  } = useRootstockWallet();

  const [modalOpen, setModalOpen] = useState(false);

  return (
    <div className=" m-4">
      {!isConnected && (
        <button className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700" onClick={() => setModalOpen(true)}>
          Connect Wallet
        </button>
      )}

      {modalOpen && !isConnected && (
        <WalletSelector onConnect={async (wallet, net) => {
          await connectWallet(wallet, net);
          setModalOpen(false);
        }} />
      )}

      {isConnected && address && (
        <WalletDropdown
          address={address}
          balance={balance}
          walletType={walletType}
          network={network}
          onDisconnect={disconnectWallet}
          onSwitch={() => switchNetwork(network === 'mainnet' ? 'testnet' : 'mainnet')}
        />
      )}
    </div>
  );
};